//
//  RandomColorSwiftTests.swift
//  RandomColorSwiftTests
//
//  Created by WANG WEI on 2015/01/22.
//  Copyright (c) 2020年 OneV's Den. All rights reserved.
//

import UIKit
import XCTest

class RandomColorSwiftTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        // Put setup code here. This method is called before the invocation of each test method in the class.
    }
    
    override func tearDown() {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
        super.tearDown()
    }
    
    func testExample() {
        // This is an example of a functional test case.
        XCTAssert(true, "Pass")
    }
    
    func testPerformanceExample() {
        // This is an example of a performance test case.
        self.measure() {
            // Put the code you want to measure the time of here.
        }
    }
    
}
