//
//  HomeView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/12.
//

import SwiftUI

struct HomeView: View {

    init() {
        AppLogger.info("HomeView 初始化", category: .ui)
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // 渐变背景
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(.systemBackground),
                        Color.indigo.opacity(0.05),
                        Color.purple.opacity(0.05)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 24) {
                        // 欢迎区域
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text(LocalizedStringKey("color_tools"))
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundStyle(
                                        LinearGradient(
                                            colors: [.indigo, .purple],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                Spacer()
                            }
                            
                            Text("Discover powerful tools for your design workflow")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 8)
                        
                        // 工具卡片网格
                        LazyVGrid(columns: [
                            GridItem(.flexible(), spacing: 16),
                            GridItem(.flexible(), spacing: 16)
                        ], spacing: 16) {
                            ToolCard(
                                icon: "paintbrush.fill",
                                title: LocalizedStringKey("gradient_presets"),
                                color: .indigo
                            )
                            
                            ToolCard(
                                icon: "circle.lefthalf.striped.horizontal",
                                title: LocalizedStringKey("color_harmony_generator"),
                                color: .purple
                            )
                            
                            ToolCard(
                                icon: "arrow.left.arrow.right",
                                title: LocalizedStringKey("color_format_converter"),
                                color: .blue
                            )
                            
                            ToolCard(
                                icon: "textformat",
                                title: LocalizedStringKey("text_readability_tester"),
                                color: .teal
                            )
                            
                            ToolCard(
                                icon: "photo.fill",
                                title: LocalizedStringKey("image_color_extractor"),
                                color: .green
                            )
                            
                            ToolCard(
                                icon: "figure.wave",
                                title: LocalizedStringKey("accessibility_checker"),
                                color: .orange
                            )
                        }
                        .padding(.horizontal, 20)
                    }
                    .padding(.top, 8)
                }
            }
            .navigationTitle(LocalizedStringKey("design_toolkit"))
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        AppLogger.debug("设置按钮被点击", category: .ui)
                        // 设置按钮动作
                    }) {
                        Image(systemName: "gearshape.fill")
                            .font(.title3)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.indigo, .purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    }
                }
            }
        }
    }
}

#Preview {
    HomeView()
}
