---
type: "always_apply"
---

1. 这是一个IOS 工程
2. 你总是使用 SwiftUI 和 Swift
3. 数据存储数据使用 SwiftData 和@AppStorage
4. SwiftUI 状态管理全部使用@Observable
5. 你总是以中文回答
6. 多线程全部使用 Swift concurrency
7. 字符串必须需要符合 IOS 国际化标准.比如字符串英文你就要翻译对应的语言.中文就对应的翻译
8.确保布局对不同 iOS 屏幕尺寸具有响应性和适应性。
9. 编写完整的单元测试
10. 一定要为 写SwiftUI 的#preview
11. 架构要符合 MVC,不要全部混杂在一个文件中
12. 不同业务的 SwiftUI 一定要放在不同的文件下
13. 每次执行完成后都需要执行xcodebuild 进行编译检查错误
14. 日志系统全部使用Logger
15.导航使用NavigationStack来实现
16.如果我们有说明,你需要使用 xcodebuild 编译检查错误并修复.你需要在最后一步才进行编译.