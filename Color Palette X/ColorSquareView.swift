//
//  ColorSquareView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/15.
//

import SwiftUI

/// 颜色方块视图组件
///
/// 显示单个颜色的方块，包括：
/// - 颜色预览
/// - 十六进制值显示
/// - 点击复制功能
/// - 现代iOS设计风格
struct ColorSquareView: View {
    
    // MARK: - 属性
    
    /// 要显示的颜色
    let color: SwiftUIColor
    
    /// 颜色类型
    let colorType: ColorType
    
    /// 是否显示十六进制值
    let showHexValue: Bool

    /// 方块大小
    let size: CGFloat
    
    /// 圆角半径
    let cornerRadius: CGFloat
    
    /// 点击状态
    @State private var isPressed = false
    
    /// 复制反馈状态
    @State private var showCopyFeedback = false
    
    // MARK: - 初始化方法
    
    /// 创建颜色方块视图
    /// - Parameters:
    ///   - color: 颜色
    ///   - colorType: 颜色类型
    ///   - showHexValue: 是否显示十六进制值，默认 true
    ///   - size: 方块大小，默认 80
    ///   - cornerRadius: 圆角半径，默认 12
    init(
        color: SwiftUIColor,
        colorType: ColorType,
        showHexValue: Bool = true,
        size: CGFloat = 80,
        cornerRadius: CGFloat = 12
    ) {
        self.color = color
        self.colorType = colorType
        self.showHexValue = showHexValue
        self.size = size
        self.cornerRadius = cornerRadius
    }
    
    // MARK: - 计算属性
    
    /// 十六进制颜色值
    private var hexValue: String {
        ColorUtils.hexString(from: color)
    }
    
    /// 是否为深色
    private var isDarkColor: Bool {
        ColorUtils.isDarkColor(color)
    }
    
    /// 文本颜色（根据背景色自动调整）
    private var textColor: SwiftUIColor {
        isDarkColor ? .white : .black
    }

    /// 阴影颜色
    private var shadowColor: SwiftUIColor {
        color.opacity(0.3)
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        Button(action: {
            copyColorToClipboard()
        }) {
            VStack(spacing: 6) {
                // 主要颜色区域
                ZStack {
                    // 背景颜色
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(color)
                        .frame(width: size, height: size)

                    // 复制反馈
                    if showCopyFeedback {
                        ZStack {
                            RoundedRectangle(cornerRadius: cornerRadius)
                                .fill(.ultraThinMaterial)
                            
                            VStack(spacing: 4) {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.green)
                                
                                Text("已复制")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.primary)
                            }
                        }
                        .frame(width: size, height: size)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .shadow(
                    color: shadowColor,
                    radius: isPressed ? 4 : 8,
                    x: 0,
                    y: isPressed ? 2 : 4
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
                
                // 颜色信息
                VStack(spacing: 2) {
                    // 十六进制值
                    if showHexValue {
                        Text(hexValue)
                            .font(.system(size: 11, weight: .semibold, design: .monospaced))
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                }
                .frame(width: size)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
            if pressing {
                AppLogger.debug("颜色方块按下: \(colorType.rawValue) - \(hexValue)", category: .ui)
            }
        }, perform: {})
    }
    
    // MARK: - 私有方法
    
    /// 复制颜色到剪贴板
    private func copyColorToClipboard() {
        AppLogger.info("复制颜色到剪贴板: \(hexValue)", category: .ui)
        
        // 复制到剪贴板
        UIPasteboard.general.string = hexValue
        
        // 显示复制反馈
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopyFeedback = true
        }
        
        // 延迟隐藏反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopyFeedback = false
            }
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}

// MARK: - 颜色行视图组件

/// 颜色行视图，显示一行四个颜色方块
struct ColorPaletteRowView: View {
    
    // MARK: - 属性
    
    /// 颜色组合数据
    let paletteRow: ColorPaletteRow
    
    /// 方块大小
    let squareSize: CGFloat
    
    /// 是否显示详细信息
    let showDetails: Bool
    
    // MARK: - 初始化方法
    
    /// 创建颜色行视图
    /// - Parameters:
    ///   - paletteRow: 颜色组合数据
    ///   - squareSize: 方块大小，默认 70
    ///   - showDetails: 是否显示详细信息，默认 true
    init(
        paletteRow: ColorPaletteRow,
        squareSize: CGFloat = 70,
        showDetails: Bool = true
    ) {
        self.paletteRow = paletteRow
        self.squareSize = squareSize
        self.showDetails = showDetails
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        HStack(spacing: 12) {
            // 基准色
            ColorSquareView(
                color: paletteRow.baseColor,
                colorType: .base,
                showHexValue: showDetails,
                size: squareSize
            )

            // 互补色
            ColorSquareView(
                color: paletteRow.complementaryColor,
                colorType: .complementary,
                showHexValue: showDetails,
                size: squareSize
            )

            // 类似色
            ColorSquareView(
                color: paletteRow.analogousColor,
                colorType: .analogous,
                showHexValue: showDetails,
                size: squareSize
            )

            // 三色调和色
            ColorSquareView(
                color: paletteRow.triadicColor,
                colorType: .triadic,
                showHexValue: showDetails,
                size: squareSize
            )
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
}

// MARK: - 预览

#if DEBUG
#Preview("ColorSquareView") {
    VStack(spacing: 20) {
        Text("颜色方块组件预览")
            .font(.title2)
            .fontWeight(.bold)
        
        // 单个颜色方块
        HStack(spacing: 16) {
            ColorSquareView(
                color: SwiftUIColor.red,
                colorType: .base
            )

            ColorSquareView(
                color: SwiftUIColor.blue,
                colorType: .complementary
            )

            ColorSquareView(
                color: SwiftUIColor.green,
                colorType: .analogous
            )

            ColorSquareView(
                color: SwiftUIColor.purple,
                colorType: .triadic
            )
        }
        
        Divider()
        
        // 完整的颜色行
        Text("完整颜色行预览")
            .font(.headline)
        
        ColorPaletteRowView(
            paletteRow: ColorPaletteRow()
        )
        
        // 紧凑模式
        Text("紧凑模式")
            .font(.headline)
        
        ColorPaletteRowView(
            paletteRow: ColorPaletteRow(),
            squareSize: 50,
            showDetails: false
        )
    }
    .padding()
}
#endif
