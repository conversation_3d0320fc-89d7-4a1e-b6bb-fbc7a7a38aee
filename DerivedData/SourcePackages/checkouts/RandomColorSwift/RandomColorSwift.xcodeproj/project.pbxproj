// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		4B1F67EC1A70F398007A07DD /* RandomColorSwiftTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F67EB1A70F398007A07DD /* RandomColorSwiftTests.swift */; };
		4B1F68011A70F3D2007A07DD /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F67F91A70F3D2007A07DD /* AppDelegate.swift */; };
		4B1F68041A70F3D2007A07DD /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4B1F67FE1A70F3D2007A07DD /* Images.xcassets */; };
		4B1F68061A70F3D2007A07DD /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F68001A70F3D2007A07DD /* ViewController.swift */; };
		4B1F68071A70F46C007A07DD /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 4B1F67FA1A70F3D2007A07DD /* LaunchScreen.xib */; };
		4B1F68081A70F46C007A07DD /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4B1F67FC1A70F3D2007A07DD /* Main.storyboard */; };
		4B1F68131A70F4BF007A07DD /* RandomColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B1F68121A70F4BF007A07DD /* RandomColor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4B1F68191A70F4BF007A07DD /* RandomColor.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4B1F680E1A70F4BF007A07DD /* RandomColor.framework */; };
		4B1F68221A70F4C0007A07DD /* RandomColorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F68211A70F4C0007A07DD /* RandomColorTests.swift */; };
		4B1F68251A70F4C0007A07DD /* RandomColor.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4B1F680E1A70F4BF007A07DD /* RandomColor.framework */; };
		4B1F68261A70F4C0007A07DD /* RandomColor.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 4B1F680E1A70F4BF007A07DD /* RandomColor.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		4B1F682F1A70F4F4007A07DD /* RandomColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F682E1A70F4F4007A07DD /* RandomColor.swift */; };
		4B1F68311A710067007A07DD /* Hue.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F68301A710067007A07DD /* Hue.swift */; };
		4B1F68331A7100A0007A07DD /* Luminosity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F68321A7100A0007A07DD /* Luminosity.swift */; };
		4B1F68351A71013B007A07DD /* ColorDefinition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1F68341A71013B007A07DD /* ColorDefinition.swift */; };
		4BA3C5D61A72081100CC3CBA /* HueExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BA3C5D51A72081100CC3CBA /* HueExtension.swift */; };
		4BC981EE1A72199600D35704 /* DetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BC981ED1A72199600D35704 /* DetailViewController.swift */; };
		4BC981F91A72244400D35704 /* NameOfColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BC981F81A72244400D35704 /* NameOfColor.swift */; };
		4BD46D0A1A71FA5B00469BDD /* SettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BD46D091A71FA5B00469BDD /* SettingViewController.swift */; };
		4BF134FD1A72427200915038 /* ntc.js in Resources */ = {isa = PBXBuildFile; fileRef = 4BF134FC1A72427200915038 /* ntc.js */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		4B1F67E61A70F398007A07DD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4B1F67C81A70F397007A07DD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4B1F67CF1A70F397007A07DD;
			remoteInfo = RandomColorSwift;
		};
		4B1F681A1A70F4BF007A07DD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4B1F67C81A70F397007A07DD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4B1F680D1A70F4BF007A07DD;
			remoteInfo = RandomColor;
		};
		4B1F681C1A70F4C0007A07DD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4B1F67C81A70F397007A07DD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4B1F67CF1A70F397007A07DD;
			remoteInfo = RandomColorSwiftDemo;
		};
		4B1F68231A70F4C0007A07DD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4B1F67C81A70F397007A07DD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4B1F680D1A70F4BF007A07DD;
			remoteInfo = RandomColor;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		4B1F682C1A70F4C0007A07DD /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				4B1F68261A70F4C0007A07DD /* RandomColor.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		4B1F67D01A70F398007A07DD /* RandomColorSwiftDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RandomColorSwiftDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4B1F67E51A70F398007A07DD /* RandomColorSwiftDemoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RandomColorSwiftDemoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		4B1F67EA1A70F398007A07DD /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4B1F67EB1A70F398007A07DD /* RandomColorSwiftTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RandomColorSwiftTests.swift; sourceTree = "<group>"; };
		4B1F67F91A70F3D2007A07DD /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		4B1F67FB1A70F3D2007A07DD /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		4B1F67FD1A70F3D2007A07DD /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		4B1F67FE1A70F3D2007A07DD /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		4B1F67FF1A70F3D2007A07DD /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4B1F68001A70F3D2007A07DD /* ViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		4B1F680E1A70F4BF007A07DD /* RandomColor.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = RandomColor.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		4B1F68111A70F4BF007A07DD /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4B1F68121A70F4BF007A07DD /* RandomColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RandomColor.h; sourceTree = "<group>"; };
		4B1F68181A70F4BF007A07DD /* RandomColorTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RandomColorTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		4B1F68201A70F4C0007A07DD /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4B1F68211A70F4C0007A07DD /* RandomColorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RandomColorTests.swift; sourceTree = "<group>"; };
		4B1F682E1A70F4F4007A07DD /* RandomColor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RandomColor.swift; sourceTree = "<group>"; };
		4B1F68301A710067007A07DD /* Hue.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Hue.swift; sourceTree = "<group>"; };
		4B1F68321A7100A0007A07DD /* Luminosity.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Luminosity.swift; sourceTree = "<group>"; };
		4B1F68341A71013B007A07DD /* ColorDefinition.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ColorDefinition.swift; sourceTree = "<group>"; };
		4BA3C5D51A72081100CC3CBA /* HueExtension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HueExtension.swift; sourceTree = "<group>"; };
		4BC981ED1A72199600D35704 /* DetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DetailViewController.swift; sourceTree = "<group>"; };
		4BC981F81A72244400D35704 /* NameOfColor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NameOfColor.swift; sourceTree = "<group>"; };
		4BD46D091A71FA5B00469BDD /* SettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingViewController.swift; sourceTree = "<group>"; };
		4BF134FC1A72427200915038 /* ntc.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; path = ntc.js; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4B1F67CD1A70F397007A07DD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1F68251A70F4C0007A07DD /* RandomColor.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F67E21A70F398007A07DD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F680A1A70F4BF007A07DD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F68151A70F4BF007A07DD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1F68191A70F4BF007A07DD /* RandomColor.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4B1F67C71A70F397007A07DD = {
			isa = PBXGroup;
			children = (
				4B1F67F81A70F3D2007A07DD /* Demo */,
				4B1F67E81A70F398007A07DD /* RandomColorSwiftTests */,
				4B1F680F1A70F4BF007A07DD /* RandomColor */,
				4B1F681E1A70F4C0007A07DD /* RandomColorTests */,
				4B1F67D11A70F398007A07DD /* Products */,
			);
			sourceTree = "<group>";
		};
		4B1F67D11A70F398007A07DD /* Products */ = {
			isa = PBXGroup;
			children = (
				4B1F67D01A70F398007A07DD /* RandomColorSwiftDemo.app */,
				4B1F67E51A70F398007A07DD /* RandomColorSwiftDemoTests.xctest */,
				4B1F680E1A70F4BF007A07DD /* RandomColor.framework */,
				4B1F68181A70F4BF007A07DD /* RandomColorTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4B1F67E81A70F398007A07DD /* RandomColorSwiftTests */ = {
			isa = PBXGroup;
			children = (
				4B1F67EB1A70F398007A07DD /* RandomColorSwiftTests.swift */,
				4B1F67E91A70F398007A07DD /* Supporting Files */,
			);
			path = RandomColorSwiftTests;
			sourceTree = "<group>";
		};
		4B1F67E91A70F398007A07DD /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				4B1F67EA1A70F398007A07DD /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		4B1F67F81A70F3D2007A07DD /* Demo */ = {
			isa = PBXGroup;
			children = (
				4BF134FB1A72427200915038 /* third-party */,
				4BD46D091A71FA5B00469BDD /* SettingViewController.swift */,
				4BC981ED1A72199600D35704 /* DetailViewController.swift */,
				4B1F67F91A70F3D2007A07DD /* AppDelegate.swift */,
				4B1F67FA1A70F3D2007A07DD /* LaunchScreen.xib */,
				4B1F67FC1A70F3D2007A07DD /* Main.storyboard */,
				4BC981F81A72244400D35704 /* NameOfColor.swift */,
				4B1F67FE1A70F3D2007A07DD /* Images.xcassets */,
				4B1F67FF1A70F3D2007A07DD /* Info.plist */,
				4B1F68001A70F3D2007A07DD /* ViewController.swift */,
				4BA3C5D51A72081100CC3CBA /* HueExtension.swift */,
			);
			path = Demo;
			sourceTree = "<group>";
		};
		4B1F680F1A70F4BF007A07DD /* RandomColor */ = {
			isa = PBXGroup;
			children = (
				4B1F68121A70F4BF007A07DD /* RandomColor.h */,
				4B1F68101A70F4BF007A07DD /* Supporting Files */,
				4B1F682E1A70F4F4007A07DD /* RandomColor.swift */,
				4B1F68341A71013B007A07DD /* ColorDefinition.swift */,
				4B1F68321A7100A0007A07DD /* Luminosity.swift */,
				4B1F68301A710067007A07DD /* Hue.swift */,
			);
			path = RandomColor;
			sourceTree = "<group>";
		};
		4B1F68101A70F4BF007A07DD /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				4B1F68111A70F4BF007A07DD /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		4B1F681E1A70F4C0007A07DD /* RandomColorTests */ = {
			isa = PBXGroup;
			children = (
				4B1F68211A70F4C0007A07DD /* RandomColorTests.swift */,
				4B1F681F1A70F4C0007A07DD /* Supporting Files */,
			);
			path = RandomColorTests;
			sourceTree = "<group>";
		};
		4B1F681F1A70F4C0007A07DD /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				4B1F68201A70F4C0007A07DD /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		4BF134FB1A72427200915038 /* third-party */ = {
			isa = PBXGroup;
			children = (
				4BF134FC1A72427200915038 /* ntc.js */,
			);
			path = "third-party";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		4B1F680B1A70F4BF007A07DD /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1F68131A70F4BF007A07DD /* RandomColor.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		4B1F67CF1A70F397007A07DD /* RandomColorSwiftDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4B1F67EF1A70F398007A07DD /* Build configuration list for PBXNativeTarget "RandomColorSwiftDemo" */;
			buildPhases = (
				4B1F67CC1A70F397007A07DD /* Sources */,
				4B1F67CD1A70F397007A07DD /* Frameworks */,
				4B1F67CE1A70F397007A07DD /* Resources */,
				4B1F682C1A70F4C0007A07DD /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				4B1F68241A70F4C0007A07DD /* PBXTargetDependency */,
			);
			name = RandomColorSwiftDemo;
			productName = RandomColorSwift;
			productReference = 4B1F67D01A70F398007A07DD /* RandomColorSwiftDemo.app */;
			productType = "com.apple.product-type.application";
		};
		4B1F67E41A70F398007A07DD /* RandomColorSwiftDemoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4B1F67F21A70F398007A07DD /* Build configuration list for PBXNativeTarget "RandomColorSwiftDemoTests" */;
			buildPhases = (
				4B1F67E11A70F398007A07DD /* Sources */,
				4B1F67E21A70F398007A07DD /* Frameworks */,
				4B1F67E31A70F398007A07DD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4B1F67E71A70F398007A07DD /* PBXTargetDependency */,
			);
			name = RandomColorSwiftDemoTests;
			productName = RandomColorSwiftTests;
			productReference = 4B1F67E51A70F398007A07DD /* RandomColorSwiftDemoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		4B1F680D1A70F4BF007A07DD /* RandomColor */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4B1F682B1A70F4C0007A07DD /* Build configuration list for PBXNativeTarget "RandomColor" */;
			buildPhases = (
				4B1F68091A70F4BF007A07DD /* Sources */,
				4B1F680A1A70F4BF007A07DD /* Frameworks */,
				4B1F680B1A70F4BF007A07DD /* Headers */,
				4B1F680C1A70F4BF007A07DD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RandomColor;
			productName = RandomColor;
			productReference = 4B1F680E1A70F4BF007A07DD /* RandomColor.framework */;
			productType = "com.apple.product-type.framework";
		};
		4B1F68171A70F4BF007A07DD /* RandomColorTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4B1F682D1A70F4C0007A07DD /* Build configuration list for PBXNativeTarget "RandomColorTests" */;
			buildPhases = (
				4B1F68141A70F4BF007A07DD /* Sources */,
				4B1F68151A70F4BF007A07DD /* Frameworks */,
				4B1F68161A70F4BF007A07DD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4B1F681B1A70F4BF007A07DD /* PBXTargetDependency */,
				4B1F681D1A70F4C0007A07DD /* PBXTargetDependency */,
			);
			name = RandomColorTests;
			productName = RandomColorTests;
			productReference = 4B1F68181A70F4BF007A07DD /* RandomColorTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4B1F67C81A70F397007A07DD /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0700;
				LastUpgradeCheck = 1030;
				ORGANIZATIONNAME = "OneV's Den";
				TargetAttributes = {
					4B1F67CF1A70F397007A07DD = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 0800;
					};
					4B1F67E41A70F398007A07DD = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 0800;
						TestTargetID = 4B1F67CF1A70F397007A07DD;
					};
					4B1F680D1A70F4BF007A07DD = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 1020;
					};
					4B1F68171A70F4BF007A07DD = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 0800;
						TestTargetID = 4B1F67CF1A70F397007A07DD;
					};
				};
			};
			buildConfigurationList = 4B1F67CB1A70F397007A07DD /* Build configuration list for PBXProject "RandomColorSwift" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 4B1F67C71A70F397007A07DD;
			productRefGroup = 4B1F67D11A70F398007A07DD /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4B1F67CF1A70F397007A07DD /* RandomColorSwiftDemo */,
				4B1F67E41A70F398007A07DD /* RandomColorSwiftDemoTests */,
				4B1F680D1A70F4BF007A07DD /* RandomColor */,
				4B1F68171A70F4BF007A07DD /* RandomColorTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4B1F67CE1A70F397007A07DD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1F68071A70F46C007A07DD /* LaunchScreen.xib in Resources */,
				4B1F68081A70F46C007A07DD /* Main.storyboard in Resources */,
				4BF134FD1A72427200915038 /* ntc.js in Resources */,
				4B1F68041A70F3D2007A07DD /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F67E31A70F398007A07DD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F680C1A70F4BF007A07DD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F68161A70F4BF007A07DD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4B1F67CC1A70F397007A07DD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4BA3C5D61A72081100CC3CBA /* HueExtension.swift in Sources */,
				4BC981F91A72244400D35704 /* NameOfColor.swift in Sources */,
				4B1F68061A70F3D2007A07DD /* ViewController.swift in Sources */,
				4BC981EE1A72199600D35704 /* DetailViewController.swift in Sources */,
				4B1F68011A70F3D2007A07DD /* AppDelegate.swift in Sources */,
				4BD46D0A1A71FA5B00469BDD /* SettingViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F67E11A70F398007A07DD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1F67EC1A70F398007A07DD /* RandomColorSwiftTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F68091A70F4BF007A07DD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1F68351A71013B007A07DD /* ColorDefinition.swift in Sources */,
				4B1F68331A7100A0007A07DD /* Luminosity.swift in Sources */,
				4B1F68311A710067007A07DD /* Hue.swift in Sources */,
				4B1F682F1A70F4F4007A07DD /* RandomColor.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B1F68141A70F4BF007A07DD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1F68221A70F4C0007A07DD /* RandomColorTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		4B1F67E71A70F398007A07DD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4B1F67CF1A70F397007A07DD /* RandomColorSwiftDemo */;
			targetProxy = 4B1F67E61A70F398007A07DD /* PBXContainerItemProxy */;
		};
		4B1F681B1A70F4BF007A07DD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4B1F680D1A70F4BF007A07DD /* RandomColor */;
			targetProxy = 4B1F681A1A70F4BF007A07DD /* PBXContainerItemProxy */;
		};
		4B1F681D1A70F4C0007A07DD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4B1F67CF1A70F397007A07DD /* RandomColorSwiftDemo */;
			targetProxy = 4B1F681C1A70F4C0007A07DD /* PBXContainerItemProxy */;
		};
		4B1F68241A70F4C0007A07DD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4B1F680D1A70F4BF007A07DD /* RandomColor */;
			targetProxy = 4B1F68231A70F4C0007A07DD /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		4B1F67FA1A70F3D2007A07DD /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				4B1F67FB1A70F3D2007A07DD /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
		4B1F67FC1A70F3D2007A07DD /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4B1F67FD1A70F3D2007A07DD /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		4B1F67ED1A70F398007A07DD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.1;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4B1F67EE1A70F398007A07DD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4B1F67F01A70F398007A07DD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				INFOPLIST_FILE = "$(SRCROOT)/Demo/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		4B1F67F11A70F398007A07DD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				INFOPLIST_FILE = "$(SRCROOT)/Demo/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		4B1F67F31A70F398007A07DD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = RandomColorSwiftTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RandomColorSwiftDemo.app/RandomColorSwiftDemo";
			};
			name = Debug;
		};
		4B1F67F41A70F398007A07DD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				INFOPLIST_FILE = RandomColorSwiftTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RandomColorSwiftDemo.app/RandomColorSwiftDemo";
			};
			name = Release;
		};
		4B1F68271A70F4C0007A07DD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = RandomColor/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 4.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		4B1F68281A70F4C0007A07DD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = RandomColor/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 4.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		4B1F68291A70F4C0007A07DD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = RandomColorTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RandomColorSwiftDemo.app/RandomColorSwiftDemo";
			};
			name = Debug;
		};
		4B1F682A1A70F4C0007A07DD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = RandomColorTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.onevcat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RandomColorSwiftDemo.app/RandomColorSwiftDemo";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4B1F67CB1A70F397007A07DD /* Build configuration list for PBXProject "RandomColorSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4B1F67ED1A70F398007A07DD /* Debug */,
				4B1F67EE1A70F398007A07DD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4B1F67EF1A70F398007A07DD /* Build configuration list for PBXNativeTarget "RandomColorSwiftDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4B1F67F01A70F398007A07DD /* Debug */,
				4B1F67F11A70F398007A07DD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4B1F67F21A70F398007A07DD /* Build configuration list for PBXNativeTarget "RandomColorSwiftDemoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4B1F67F31A70F398007A07DD /* Debug */,
				4B1F67F41A70F398007A07DD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4B1F682B1A70F4C0007A07DD /* Build configuration list for PBXNativeTarget "RandomColor" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4B1F68271A70F4C0007A07DD /* Debug */,
				4B1F68281A70F4C0007A07DD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4B1F682D1A70F4C0007A07DD /* Build configuration list for PBXNativeTarget "RandomColorTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4B1F68291A70F4C0007A07DD /* Debug */,
				4B1F682A1A70F4C0007A07DD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 4B1F67C81A70F397007A07DD /* Project object */;
}
