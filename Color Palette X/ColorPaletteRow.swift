//
//  ColorPaletteRow.swift
//  Color Palette X
//
//  Created by JackFan on 2025/7/15.
//

import SwiftUI
import RandomColor

/// 颜色组合数据模型
/// 
/// 表示一行颜色调色板，包含四种基于色彩理论的颜色组合：
/// - 基准色：使用 RandomColorSwift 生成的随机颜色
/// - 互补色：色相环上相对180度的颜色
/// - 类似色：色相环上相邻±30度的颜色  
/// - 三色调和色：色相环上相隔120度的颜色
struct ColorPaletteRow: Identifiable, Equatable {
    
    // MARK: - 属性
    
    /// 唯一标识符
    let id = UUID()
    
    /// 基准色（随机生成）
    let baseColor: SwiftUIColor

    /// 互补色（基于基准色计算）
    let complementaryColor: SwiftUIColor

    /// 类似色（基于基准色计算）
    let analogousColor: SwiftUIColor

    /// 三色调和色（基于基准色计算）
    let triadicColor: SwiftUIColor
    
    /// 创建时间戳
    let createdAt: Date
    
    // MARK: - 初始化方法
    
    /// 使用指定的基准色创建颜色组合
    /// - Parameter baseColor: 基准颜色
    init(baseColor: SwiftUIColor) {
        AppLogger.debug("创建颜色组合，基准色: \(ColorUtils.hexString(from: baseColor))", category: .general)
        
        self.baseColor = baseColor
        self.complementaryColor = ColorUtils.complementaryColor(from: baseColor)
        self.analogousColor = ColorUtils.analogousColor(from: baseColor)
        self.triadicColor = ColorUtils.triadicColor(from: baseColor)
        self.createdAt = Date()
        
        AppLogger.debug("颜色组合创建完成", category: .general)
    }
    
    /// 使用 RandomColorSwift 生成随机基准色并创建颜色组合
    /// - Parameters:
    ///   - hue: 色相偏好（可选）
    ///   - luminosity: 亮度偏好（可选）
    init(randomHue hue: Hue = .random, luminosity: Luminosity = .random) {
        AppLogger.debug("使用 RandomColorSwift 生成随机颜色组合", category: .general)

        // 使用 RandomColorSwift 生成随机颜色
        let randomUIColor = randomColor(hue: hue, luminosity: luminosity)

        // 将 UIColor 转换为 SwiftUI Color
        let baseColor = SwiftUIColor(randomUIColor)

        // 直接设置属性而不是调用其他初始化方法
        AppLogger.debug("开始计算颜色组合，基准色: \(ColorUtils.hexString(from: baseColor))", category: .general)

        self.baseColor = baseColor
        self.complementaryColor = ColorUtils.complementaryColor(from: baseColor)
        self.analogousColor = ColorUtils.analogousColor(from: baseColor)
        self.triadicColor = ColorUtils.triadicColor(from: baseColor)
        self.createdAt = Date()

        AppLogger.debug("随机颜色组合生成完成，基准色: \(ColorUtils.hexString(from: baseColor))", category: .general)
    }

    /// 使用默认随机设置创建颜色组合的便利初始化方法
    init() {
        self.init(randomHue: .random, luminosity: .random)
    }

    // MARK: - 静态方法
    
    /// 从十六进制字符串创建 SwiftUI Color
    /// - Parameter hexString: 十六进制颜色字符串（如 "#FF5733" 或 "FF5733"）
    /// - Returns: 对应的 SwiftUI Color，如果解析失败返回 nil
    private static func colorFromHexString(_ hexString: String) -> SwiftUIColor? {
        var hex = hexString.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 移除 # 前缀
        if hex.hasPrefix("#") {
            hex.removeFirst()
        }
        
        // 确保是6位十六进制
        guard hex.count == 6 else {
            AppLogger.warning("无效的十六进制颜色字符串: \(hexString)", category: .general)
            return nil
        }
        
        // 解析RGB值
        guard let rgbValue = UInt32(hex, radix: 16) else {
            AppLogger.warning("无法解析十六进制颜色: \(hexString)", category: .general)
            return nil
        }
        
        let red = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let green = Double((rgbValue & 0x00FF00) >> 8) / 255.0
        let blue = Double(rgbValue & 0x0000FF) / 255.0
        
        return SwiftUIColor(red: red, green: green, blue: blue)
    }
    
    /// 批量生成指定数量的随机颜色组合
    /// - Parameter count: 要生成的颜色组合数量
    /// - Returns: 颜色组合数组
    static func generateRandomPalettes(count: Int) -> [ColorPaletteRow] {
        guard count > 0 else {
            AppLogger.warning("请求生成的颜色组合数量无效: \(count)", category: .general)
            return []
        }

        guard count <= 1000 else {
            AppLogger.warning("请求生成的颜色组合数量过大: \(count)，限制为1000", category: .general)
            return generateRandomPalettes(count: 1000)
        }

        AppLogger.info("开始批量生成 \(count) 个随机颜色组合", category: .general)

        var palettes: [ColorPaletteRow] = []
        palettes.reserveCapacity(count) // 预分配内存以提高性能

        // 定义多种色相和亮度组合以增加多样性
        let hueOptions: [Hue] = [.red, .orange, .yellow, .green, .blue, .purple, .pink, .monochrome, .random]
        let luminosityOptions: [Luminosity] = [.bright, .light, .dark, .random]

        // 分批处理以避免内存峰值
        let batchSize = min(50, count)
        let batches = (count + batchSize - 1) / batchSize

        for batchIndex in 0..<batches {
            let startIndex = batchIndex * batchSize
            let endIndex = min(startIndex + batchSize, count)

            autoreleasepool {
                for i in startIndex..<endIndex {
                    let hue = hueOptions[i % hueOptions.count]
                    let luminosity = luminosityOptions[i % luminosityOptions.count]

                    let palette = ColorPaletteRow(randomHue: hue, luminosity: luminosity)
                    palettes.append(palette)
                }
            }

            // 在批次之间添加短暂延迟以避免阻塞主线程
            if batchIndex < batches - 1 {
                Thread.sleep(forTimeInterval: 0.001)
            }
        }

        AppLogger.info("批量生成完成，共生成 \(palettes.count) 个颜色组合", category: .general)

        return palettes
    }
    
    // MARK: - 计算属性
    
    /// 获取所有颜色的数组
    var allColors: [SwiftUIColor] {
        return [baseColor, complementaryColor, analogousColor, triadicColor]
    }
    
    /// 获取所有颜色的十六进制字符串
    var allHexStrings: [String] {
        return allColors.map { ColorUtils.hexString(from: $0) }
    }
    
    /// 获取颜色组合的描述信息
    var description: String {
        let hexStrings = allHexStrings
        return "基准色: \(hexStrings[0]), 互补色: \(hexStrings[1]), 类似色: \(hexStrings[2]), 三色调和色: \(hexStrings[3])"
    }
    
    // MARK: - Equatable 协议实现
    
    static func == (lhs: ColorPaletteRow, rhs: ColorPaletteRow) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - 颜色类型枚举

/// 颜色类型枚举，用于标识颜色在组合中的角色
enum ColorType: String, CaseIterable {
    case base = "基准色"
    case complementary = "互补色"
    case analogous = "类似色"
    case triadic = "三色调和色"
    
    /// 颜色类型的英文标识
    var identifier: String {
        switch self {
        case .base: return "base"
        case .complementary: return "complementary"
        case .analogous: return "analogous"
        case .triadic: return "triadic"
        }
    }
    
    /// 颜色类型的图标
    var icon: String {
        switch self {
        case .base: return "circle.fill"
        case .complementary: return "arrow.triangle.2.circlepath"
        case .analogous: return "arrow.left.and.right.circle"
        case .triadic: return "triangle.fill"
        }
    }
    
    /// 颜色类型的描述
    var description: String {
        switch self {
        case .base:
            return "随机生成的基准颜色"
        case .complementary:
            return "色相环上相对180度的颜色，具有强烈对比"
        case .analogous:
            return "色相环上相邻的颜色，和谐统一"
        case .triadic:
            return "色相环上等距分布的颜色，平衡而有活力"
        }
    }
}

// MARK: - 预览和测试

#if DEBUG
/// ColorPaletteRow 预览和测试
struct ColorPaletteRowPreview {
    
    /// 测试颜色组合生成
    static func testPaletteGeneration() {
        AppLogger.info("开始测试颜色组合生成", category: .general)
        
        // 测试单个颜色组合
        let singlePalette = ColorPaletteRow()
        AppLogger.info("单个组合: \(singlePalette.description)", category: .general)
        
        // 测试批量生成
        let multiplePalettes = ColorPaletteRow.generateRandomPalettes(count: 5)
        for (index, palette) in multiplePalettes.enumerated() {
            AppLogger.info("组合 \(index + 1): \(palette.description)", category: .general)
        }
    }
}

#Preview("ColorPaletteRow Test") {
    VStack(spacing: 20) {
        Text("颜色组合数据模型测试")
            .font(.title)
            .fontWeight(.bold)
        
        Button("测试颜色组合生成") {
            ColorPaletteRowPreview.testPaletteGeneration()
        }
        .buttonStyle(.borderedProminent)
        
        Text("查看 Xcode 控制台输出")
            .font(.caption)
            .foregroundColor(.secondary)
    }
    .padding()
}
#endif
