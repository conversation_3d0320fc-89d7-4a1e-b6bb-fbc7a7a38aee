//
//  ColorUtils.swift
//  Color Palette X
//
//  Created by JackFan on 2025/7/15.
//

import SwiftUI
import UIKit

/// 颜色工具类
/// 
/// 提供各种颜色计算和转换功能，包括：
/// - 互补色生成（色相环上相对180度的颜色）
/// - 类似色生成（色相环上相邻±30度的颜色）
/// - 三色调和色生成（色相环上相隔120度的颜色）
/// 
/// 所有方法都基于HSB（色相、饱和度、亮度）颜色模型进行计算
/// 确保生成的颜色在视觉上和谐且具有良好的对比度
public final class ColorUtils {
    
    // MARK: - 私有初始化
    
    /// 防止实例化，所有方法都是静态方法
    private init() {}
    
    // MARK: - 颜色转换辅助方法
    
    /// 将 SwiftUI Color 转换为 UIColor
    /// - Parameter color: SwiftUI Color 对象
    /// - Returns: 对应的 UIColor 对象
    private static func uiColor(from color: SwiftUIColor) -> UIColor {
        return UIColor(color)
    }

    /// 将 UIColor 转换为 SwiftUI Color
    /// - Parameter uiColor: UIColor 对象
    /// - Returns: 对应的 SwiftUI Color 对象
    private static func swiftUIColor(from uiColor: UIColor) -> SwiftUIColor {
        return SwiftUIColor(uiColor)
    }
    
    /// 从 UIColor 提取 HSB 值
    /// - Parameter color: UIColor 对象
    /// - Returns: 包含色相、饱和度、亮度、透明度的元组
    private static func extractHSB(from color: UIColor) -> (hue: CGFloat, saturation: CGFloat, brightness: CGFloat, alpha: CGFloat) {
        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0
        
        color.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
        
        return (hue: hue, saturation: saturation, brightness: brightness, alpha: alpha)
    }
    
    /// 标准化色相值到 0-1 范围
    /// - Parameter hue: 原始色相值
    /// - Returns: 标准化后的色相值（0-1）
    private static func normalizeHue(_ hue: CGFloat) -> CGFloat {
        var normalizedHue = hue
        while normalizedHue < 0 {
            normalizedHue += 1.0
        }
        while normalizedHue >= 1.0 {
            normalizedHue -= 1.0
        }
        return normalizedHue
    }
    
    // MARK: - 公共颜色生成方法
    
    /// 生成互补色
    /// 
    /// 互补色是色相环上相对180度的颜色，具有最强烈的对比效果。
    /// 算法原理：
    /// 1. 提取基准色的HSB值
    /// 2. 将色相值增加0.5（相当于180度）
    /// 3. 保持饱和度和亮度不变
    /// 4. 如果色相值超过1.0，则减去1.0实现环形计算
    /// 
    /// 应用场景：
    /// - 强调重点内容
    /// - 创建视觉冲击力
    /// - 按钮和背景的高对比搭配
    /// 
    /// - Parameter baseColor: 基准颜色
    /// - Returns: 基准色的互补色
    public static func complementaryColor(from baseColor: SwiftUIColor) -> SwiftUIColor {
        AppLogger.debug("开始计算互补色", category: .general)
        
        let uiColor = uiColor(from: baseColor)
        let hsb = extractHSB(from: uiColor)
        
        // 计算互补色相：增加180度（0.5）
        let complementaryHue = normalizeHue(hsb.hue + 0.5)
        
        // 创建互补色，保持饱和度和亮度
        let complementaryUIColor = UIColor(
            hue: complementaryHue,
            saturation: hsb.saturation,
            brightness: hsb.brightness,
            alpha: hsb.alpha
        )
        
        AppLogger.debug("互补色计算完成，原色相: \(hsb.hue), 新色相: \(complementaryHue)", category: .general)
        
        return swiftUIColor(from: complementaryUIColor)
    }
    
    /// 生成类似色
    /// 
    /// 类似色是色相环上相邻的颜色，通常相差30度左右，产生和谐统一的视觉效果。
    /// 算法原理：
    /// 1. 提取基准色的HSB值
    /// 2. 随机选择向左或向右偏移30度（0.083）
    /// 3. 保持饱和度和亮度基本不变（可微调）
    /// 4. 确保色相值在有效范围内
    /// 
    /// 应用场景：
    /// - 渐变背景
    /// - 柔和的配色方案
    /// - 同一主题下的不同元素
    /// 
    /// - Parameter baseColor: 基准颜色
    /// - Returns: 基准色的类似色
    public static func analogousColor(from baseColor: SwiftUIColor) -> SwiftUIColor {
        AppLogger.debug("开始计算类似色", category: .general)
        
        let uiColor = uiColor(from: baseColor)
        let hsb = extractHSB(from: uiColor)
        
        // 随机选择偏移方向：+30度或-30度（±0.083）
        let offset: CGFloat = Bool.random() ? 0.083 : -0.083
        let analogousHue = normalizeHue(hsb.hue + offset)
        
        // 微调饱和度和亮度以增加变化
        let adjustedSaturation = max(0.1, min(1.0, hsb.saturation + CGFloat.random(in: -0.1...0.1)))
        let adjustedBrightness = max(0.2, min(1.0, hsb.brightness + CGFloat.random(in: -0.1...0.1)))
        
        let analogousUIColor = UIColor(
            hue: analogousHue,
            saturation: adjustedSaturation,
            brightness: adjustedBrightness,
            alpha: hsb.alpha
        )
        
        AppLogger.debug("类似色计算完成，偏移: \(offset), 新色相: \(analogousHue)", category: .general)
        
        return swiftUIColor(from: analogousUIColor)
    }
    
    /// 生成三色调和色
    /// 
    /// 三色调和色是色相环上等距分布的三个颜色之一，相互间隔120度。
    /// 这种配色方案既有对比又保持平衡，是最经典的配色理论之一。
    /// 算法原理：
    /// 1. 提取基准色的HSB值
    /// 2. 随机选择+120度或+240度（相当于-120度）
    /// 3. 适当调整饱和度和亮度以增加层次感
    /// 4. 确保生成的颜色具有良好的视觉平衡
    /// 
    /// 应用场景：
    /// - 品牌配色方案
    /// - 图表和数据可视化
    /// - 平衡而有活力的设计
    /// 
    /// - Parameter baseColor: 基准颜色
    /// - Returns: 基准色的三色调和色
    public static func triadicColor(from baseColor: SwiftUIColor) -> SwiftUIColor {
        AppLogger.debug("开始计算三色调和色", category: .general)
        
        let uiColor = uiColor(from: baseColor)
        let hsb = extractHSB(from: uiColor)
        
        // 随机选择三色调和的两个位置：+120度或+240度
        let offset: CGFloat = Bool.random() ? 0.333 : 0.667  // 120/360 = 0.333, 240/360 = 0.667
        let triadicHue = normalizeHue(hsb.hue + offset)
        
        // 调整饱和度和亮度以创造更丰富的视觉效果
        let adjustedSaturation = max(0.3, min(1.0, hsb.saturation + CGFloat.random(in: -0.2...0.2)))
        let adjustedBrightness = max(0.3, min(0.9, hsb.brightness + CGFloat.random(in: -0.2...0.2)))
        
        let triadicUIColor = UIColor(
            hue: triadicHue,
            saturation: adjustedSaturation,
            brightness: adjustedBrightness,
            alpha: hsb.alpha
        )
        
        AppLogger.debug("三色调和色计算完成，偏移: \(offset), 新色相: \(triadicHue)", category: .general)
        
        return swiftUIColor(from: triadicUIColor)
    }
    
    // MARK: - 辅助工具方法
    
    /// 获取颜色的十六进制字符串表示
    /// - Parameter color: SwiftUI Color 对象
    /// - Returns: 十六进制颜色字符串（如 "#FF5733"）
    public static func hexString(from color: SwiftUIColor) -> String {
        let uiColor = uiColor(from: color)
        
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        let r = Int(red * 255)
        let g = Int(green * 255)
        let b = Int(blue * 255)
        
        return String(format: "#%02X%02X%02X", r, g, b)
    }
    
    /// 判断颜色是否为深色
    /// - Parameter color: SwiftUI Color 对象
    /// - Returns: 如果是深色返回 true，否则返回 false
    public static func isDarkColor(_ color: SwiftUIColor) -> Bool {
        let uiColor = uiColor(from: color)
        let hsb = extractHSB(from: uiColor)
        
        // 亮度小于0.5认为是深色
        return hsb.brightness < 0.5
    }
}

// MARK: - 预览和测试

#if DEBUG
/// ColorUtils 预览和测试
struct ColorUtilsPreview {
    
    /// 测试所有颜色生成方法
    static func testColorGeneration() {
        let baseColor = SwiftUIColor.blue
        
        AppLogger.info("开始测试颜色生成功能", category: .general)
        
        let complementary = ColorUtils.complementaryColor(from: baseColor)
        let analogous = ColorUtils.analogousColor(from: baseColor)
        let triadic = ColorUtils.triadicColor(from: baseColor)
        
        AppLogger.info("基准色: \(ColorUtils.hexString(from: baseColor))", category: .general)
        AppLogger.info("互补色: \(ColorUtils.hexString(from: complementary))", category: .general)
        AppLogger.info("类似色: \(ColorUtils.hexString(from: analogous))", category: .general)
        AppLogger.info("三色调和色: \(ColorUtils.hexString(from: triadic))", category: .general)
    }
}

#Preview("ColorUtils Test") {
    VStack(spacing: 20) {
        Text("颜色工具类测试")
            .font(.title)
            .fontWeight(.bold)
        
        Button("测试颜色生成") {
            ColorUtilsPreview.testColorGeneration()
        }
        .buttonStyle(.borderedProminent)
        
        Text("查看 Xcode 控制台输出")
            .font(.caption)
            .foregroundColor(.secondary)
    }
    .padding()
}
#endif
