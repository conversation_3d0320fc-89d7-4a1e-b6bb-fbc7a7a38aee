//
//  TestColorGeneration.swift
//  Color Palette X
//
//  Created by JackFan on 2025/7/15.
//

import SwiftUI
import RandomColor

/// 测试颜色生成功能的简单验证
struct TestColorGeneration {
    
    /// 测试颜色工具类的所有方法
    static func testColorUtils() {
        print("🎨 开始测试颜色工具类...")
        
        // 测试基准色
        let baseColor = SwiftUIColor.blue
        print("基准色: \(ColorUtils.hexString(from: baseColor))")
        
        // 测试互补色
        let complementary = ColorUtils.complementaryColor(from: baseColor)
        print("互补色: \(ColorUtils.hexString(from: complementary))")
        
        // 测试类似色
        let analogous = ColorUtils.analogousColor(from: baseColor)
        print("类似色: \(ColorUtils.hexString(from: analogous))")
        
        // 测试三色调和色
        let triadic = ColorUtils.triadicColor(from: baseColor)
        print("三色调和色: \(ColorUtils.hexString(from: triadic))")
        
        print("✅ 颜色工具类测试完成")
    }
    
    /// 测试颜色组合生成
    static func testColorPaletteRow() {
        print("🎨 开始测试颜色组合生成...")
        
        // 测试单个颜色组合
        let palette = ColorPaletteRow()
        print("颜色组合: \(palette.description)")
        
        // 测试批量生成
        let palettes = ColorPaletteRow.generateRandomPalettes(count: 5)
        print("批量生成了 \(palettes.count) 个颜色组合")
        
        for (index, palette) in palettes.enumerated() {
            print("组合 \(index + 1): \(palette.allHexStrings.joined(separator: ", "))")
        }
        
        print("✅ 颜色组合生成测试完成")
    }
    
    /// 测试 RandomColorSwift 集成
    static func testRandomColorIntegration() {
        print("🎨 开始测试 RandomColorSwift 集成...")
        
        // 测试不同的色相和亮度
        let hues: [Hue] = [.red, .blue, .green, .random]
        let luminosities: [Luminosity] = [.bright, .dark, .random]
        
        for hue in hues {
            for luminosity in luminosities {
                let palette = ColorPaletteRow(randomHue: hue, luminosity: luminosity)
                print("色相: \(hue), 亮度: \(luminosity) -> \(ColorUtils.hexString(from: palette.baseColor))")
            }
        }
        
        print("✅ RandomColorSwift 集成测试完成")
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始运行所有颜色生成测试...")
        print("=" * 50)
        
        testColorUtils()
        print("")
        
        testColorPaletteRow()
        print("")
        
        testRandomColorIntegration()
        print("")
        
        print("=" * 50)
        print("🎉 所有测试完成！")
    }
}

// MARK: - 字符串扩展（用于重复字符）
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// MARK: - 预览
#if DEBUG
#Preview("Color Generation Test") {
    VStack(spacing: 20) {
        Text("颜色生成功能测试")
            .font(.title)
            .fontWeight(.bold)
        
        Button("运行所有测试") {
            TestColorGeneration.runAllTests()
        }
        .buttonStyle(.borderedProminent)
        
        Text("查看 Xcode 控制台输出")
            .font(.caption)
            .foregroundColor(.secondary)
        
        Spacer()
    }
    .padding()
}
#endif
